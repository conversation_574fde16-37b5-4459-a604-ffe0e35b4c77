# Sistema di Metriche Business con OpenTelemetry e Prometheus

Questo progetto implementa un sistema semplice e flessibile per raccogliere metriche di business logic e inviarle a Prometheus tramite OTLP (OpenTelemetry Protocol).

## Caratteristiche

- ✅ **Semplice come un logger**: Un'unica interfaccia per registrare eventi
- ✅ **Massima flessibilità**: Supporta metriche personalizzate con metadati
- ✅ **Real-time**: Le metriche sono visibili immediatamente in Prometheus/Grafana
- ✅ **Filtrabile**: Usa i metadati per creare filtri e dashboard dettagliate
- ✅ **Tipi di metriche**: Counter, Histogram, Gauge
- ✅ **Zero configurazione**: Funziona out-of-the-box con configurazione minima

## Installazione

Il sistema è già configurato e pronto all'uso. Le dipendenze installate:

```bash
composer require open-telemetry/symfony-sdk-bundle
```

## Configurazione

### 1. Variabili di ambiente (.env.local)

```env
# Endpoint del collector OTLP (Prometheus o collector intermedio)
OTEL_EXPORTER_OTLP_ENDPOINT=http://parche.sbx.telemetry:4318
OTEL_SERVICE_NAME=symfony-otlp-app
OTEL_SERVICE_VERSION=1.0.0
OTEL_EXPORTER_OTLP_HEADERS={}
```

### 2. Configurazione OpenTelemetry (config/packages/otel_sdk.yaml)

La configurazione è ottimizzata per inviare solo metriche (no traces/logs) a Prometheus.

## Utilizzo

### Metodo Base - Generico

```php
use App\Service\MetricsCollector;

class YourService
{
    public function __construct(
        private MetricsCollector $metricsCollector
    ) {}

    public function someBusinessLogic(): void
    {
        // Registra un evento semplice
        $this->metricsCollector->recordEvent('user_registration', 1, [
            'user_type' => 'premium',
            'source' => 'web'
        ]);

        // Registra una durata
        $this->metricsCollector->recordEvent('api_response_time', 150.5, [
            'endpoint' => '/api/users',
            'method' => 'POST'
        ], 'histogram');

        // Registra un valore corrente
        $this->metricsCollector->recordEvent('active_users', 42, [
            'region' => 'europe'
        ], 'gauge');
    }
}
```

### Metodi di Convenienza

```php
// Registrazione utente
$this->metricsCollector->recordUserRegistration('premium', [
    'source' => 'mobile_app',
    'country' => 'IT'
]);

// Login
$this->metricsCollector->recordLogin('success', [
    'method' => 'oauth',
    'provider' => 'google'
]);

// Invio email
$this->metricsCollector->recordEmailSent('welcome', [
    'template' => 'welcome_v2',
    'language' => 'it'
]);

// Eventi di business generici
$this->metricsCollector->recordBusinessEvent('order_completed', 1, [
    'payment_method' => 'credit_card',
    'order_value' => 'high'
]);

// Durate/Performance
$this->metricsCollector->recordDuration('database_query', 25.3, [
    'query_type' => 'select',
    'table' => 'users'
]);
```

## Tipi di Metriche

### 1. Counter (default)
Per contare eventi che accadono nel tempo:
- Registrazioni utente
- Login
- Email inviate
- Errori

### 2. Histogram
Per misurare distribuzioni di valori:
- Tempi di risposta
- Durate di operazioni
- Dimensioni di file

### 3. Gauge
Per valori che possono aumentare/diminuire:
- Utenti attivi
- Memoria utilizzata
- Code di elaborazione

## Esempi Pratici

### Controller
```php
#[Route('/api/user/register', methods: ['POST'])]
public function register(Request $request): JsonResponse
{
    $userData = json_decode($request->getContent(), true);
    
    try {
        $user = $this->userService->createUser($userData);
        
        // Metrica di successo
        $this->metricsCollector->recordUserRegistration(
            $userData['type'] ?? 'standard',
            ['source' => 'api']
        );
        
        return new JsonResponse(['success' => true]);
        
    } catch (\Exception $e) {
        // Metrica di errore
        $this->metricsCollector->recordEvent('user_registration_failed', 1, [
            'error_type' => get_class($e)
        ]);
        
        return new JsonResponse(['success' => false], 500);
    }
}
```

### Service
```php
public function processOrder(array $orderData): void
{
    $startTime = microtime(true);
    
    try {
        // Logica di business
        $this->processPayment($orderData);
        $this->updateInventory($orderData);
        
        $duration = (microtime(true) - $startTime) * 1000;
        
        // Metriche di successo
        $this->metricsCollector->recordBusinessEvent('order_processed', 1, [
            'payment_method' => $orderData['payment_method'],
            'total_items' => count($orderData['items'])
        ]);
        
        $this->metricsCollector->recordDuration('order_processing', $duration);
        
    } catch (\Exception $e) {
        // Metrica di errore
        $this->metricsCollector->recordEvent('order_processing_failed', 1, [
            'error_type' => get_class($e),
            'step' => $this->getCurrentProcessingStep()
        ]);
        
        throw $e;
    }
}
```

## Visualizzazione in Prometheus/Grafana

Le metriche saranno disponibili in Prometheus con nomi come:
- `user_registration_total{user_type="premium", source="web"}`
- `email_sent_total{email_type="welcome", template="v2"}`
- `api_response_time_bucket{endpoint="/api/users", method="POST"}`
- `active_users{region="europe"}`

### Query Prometheus di esempio:
```promql
# Registrazioni per tipo utente nell'ultima ora
rate(user_registration_total[1h]) by (user_type)

# Tempo medio di risposta API
histogram_quantile(0.95, rate(api_response_time_bucket[5m]))

# Email inviate per tipo
sum(rate(email_sent_total[1h])) by (email_type)
```

## Test

Per testare il sistema:

### 1. Test via Console Command
```bash
# Testa tutte le funzionalità delle metriche
php bin/console app:test-metrics
```

### 2. Test via API
```bash
# Avvia il server Symfony
php -S localhost:8000 -t public

# Testa le metriche via API
curl -X GET http://localhost:8000/api/metrics/test
curl -X POST http://localhost:8000/api/user/register -H "Content-Type: application/json" -d '{"type":"premium","source":"api","country":"IT"}'
curl -X POST http://localhost:8000/api/user/login -H "Content-Type: application/json" -d '{"email":"<EMAIL>","method":"email"}'
curl -X POST http://localhost:8000/api/email/send -H "Content-Type: application/json" -d '{"type":"welcome","template":"v2"}'
```

### 3. Configurazione per Prometheus
Per abilitare l'invio a Prometheus, decommentare la riga nell'`.env.local`:
```env
OTEL_EXPORTER_OTLP_ENDPOINT=http://parche.sbx.telemetry:4318
```

## Vantaggi

1. **Semplicità**: Un'unica interfaccia per tutte le metriche
2. **Flessibilità**: Metadati personalizzabili per ogni evento
3. **Performance**: Invio asincrono delle metriche
4. **Scalabilità**: Supporta high-throughput applications
5. **Observability**: Visibilità completa degli eventi di business
6. **Debugging**: Log automatico degli errori di invio metriche
