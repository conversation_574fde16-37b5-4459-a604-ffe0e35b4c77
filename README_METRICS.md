# Sistema di Metriche Business con OpenTelemetry e Prometheus

Questo progetto implementa un sistema semplice e flessibile per raccogliere metriche di business logic e inviarle a Prometheus tramite OTLP (OpenTelemetry Protocol).

## Caratteristiche

- ✅ **Semplice come un logger**: Un'unica interfaccia per registrare eventi
- ✅ **Massima flessibilità**: Supporta metriche personalizzate con metadati
- ✅ **Real-time**: Le metriche sono visibili immediatamente in Prometheus/Grafana
- ✅ **Filtrabile**: Usa i metadati per creare filtri e dashboard dettagliate
- ✅ **Tipi di metriche**: Counter, Histogram, Gauge
- ✅ **Zero configurazione**: Funziona out-of-the-box con configurazione minima

## Installazione

Il sistema è già configurato e pronto all'uso. Le dipendenze installate:

```bash
composer require open-telemetry/symfony-sdk-bundle
```

## Configurazione

### 1. Variabili di ambiente (.env.local)

```env
# Endpoint del collector OTLP (Prometheus o collector intermedio)
OTEL_EXPORTER_OTLP_ENDPOINT=http://parche.sbx.telemetry:4318
OTEL_SERVICE_NAME=symfony-otlp-app
OTEL_SERVICE_VERSION=1.0.0
OTEL_EXPORTER_OTLP_HEADERS={}
```

### 2. Configurazione OpenTelemetry (config/packages/otel_sdk.yaml)

La configurazione è ottimizzata per inviare solo metriche (no traces/logs) a Prometheus.

## Utilizzo

### Metodo Base - Generico

```php
use App\Service\MetricsCollector;

class YourService
{
    public function __construct(
        private MetricsCollector $metricsCollector
    ) {}

    public function someBusinessLogic(): void
    {
        // Registra un evento semplice
        $this->metricsCollector->recordEvent('user_registration', 1, [
            'user_type' => 'premium',
            'source' => 'web'
        ]);

        // Registra una durata
        $this->metricsCollector->recordEvent('api_response_time', 150.5, [
            'endpoint' => '/api/users',
            'method' => 'POST'
        ], 'histogram');

        // Registra un valore corrente
        $this->metricsCollector->recordEvent('active_users', 42, [
            'region' => 'europe'
        ], 'gauge');
    }
}
```

### Esempi di Utilizzo

```php
// Registrazione utente
$this->metricsCollector->recordEvent('user_registration', 1, [
    'user_type' => 'premium',
    'source' => 'mobile_app',
    'country' => 'IT'
]);

// Login
$this->metricsCollector->recordEvent('user_login', 1, [
    'status' => 'success',
    'method' => 'oauth',
    'provider' => 'google'
]);

// Invio email
$this->metricsCollector->recordEvent('email_sent', 1, [
    'email_type' => 'welcome',
    'template' => 'welcome_v2',
    'language' => 'it'
]);

// Eventi di business
$this->metricsCollector->recordEvent('business_order_completed', 1, [
    'payment_method' => 'credit_card',
    'order_value' => 'high'
]);

// Durate/Performance (histogram)
$this->metricsCollector->recordEvent('database_query_duration', 25.3, [
    'query_type' => 'select',
    'table' => 'users'
], 'histogram');
```

## Tipi di Metriche

### 1. Counter (default)
Per contare eventi che accadono nel tempo:
- Registrazioni utente
- Login
- Email inviate
- Errori

### 2. Histogram
Per misurare distribuzioni di valori:
- Tempi di risposta
- Durate di operazioni
- Dimensioni di file

### 3. Gauge
Per valori che possono aumentare/diminuire:
- Utenti attivi
- Memoria utilizzata
- Code di elaborazione

## Esempi Pratici





## Visualizzazione in Prometheus/Grafana

Le metriche saranno disponibili in Prometheus con nomi come:
- `user_registration_total{user_type="premium", source="web"}`
- `email_sent_total{email_type="welcome", template="v2"}`
- `api_response_time_bucket{endpoint="/api/users", method="POST"}`
- `active_users{region="europe"}`

### Query Prometheus di esempio:
```promql
# Registrazioni per tipo utente nell'ultima ora
rate(user_registration_total[1h]) by (user_type)

# Tempo medio di risposta API
histogram_quantile(0.95, rate(api_response_time_bucket[5m]))

# Email inviate per tipo
sum(rate(email_sent_total[1h])) by (email_type)
```

## Test del POC

### 🧪 Test Principale via Console Command
```bash
# Testa tutte le funzionalità delle metriche (raccomandato per POC)
php bin/console app:test-metrics
```

Questo comando testa:
- Counter metrics (registrazione, login, email)
- Business events (ordini, azioni)
- Histogram metrics (durate, performance)
- Gauge metrics (valori correnti)



### 📊 Configurazione per Prometheus
Per abilitare l'invio a Prometheus, decommentare la riga nell'`.env.local`:
```env
OTEL_EXPORTER_OTLP_ENDPOINT=http://parche.sbx.telemetry:4318
```

## Vantaggi

1. **Massima Semplicità**: Un solo metodo `recordEvent()` per tutte le metriche
2. **Zero Codice Ridondante**: Nessun metodo di convenienza inutile
3. **Flessibilità Totale**: Metadati personalizzabili per ogni evento
4. **Performance**: Invio asincrono delle metriche
5. **Scalabilità**: Supporta high-throughput applications
6. **Observability**: Visibilità completa degli eventi di business
7. **Debugging**: Console exporter per sviluppo + log automatico errori

## 📊 Dashboard Grafana

Per visualizzare le metriche in Grafana:

1. **Importa la dashboard**: Usa il file `grafana-dashboard.json` incluso nel progetto
2. **Configura Prometheus**: Assicurati che Prometheus sia configurato per raccogliere le metriche OTLP
3. **Visualizza le metriche**: La dashboard include:
   - **User Registration Events**: Counter con filtri per tipo utente e sorgente
   - **User Login Events**: Timeline dei login con status e metodo
   - **Email Sent Events**: Bar chart degli invii email per tipo e template
   - **Business Events**: Timeline degli eventi di business
   - **API Call Duration**: Histogram con percentili (p50, p95, p99)
   - **Test Gauge Values**: Gauge per valori correnti
   - **All POC Metrics Overview**: Tabella con tutte le metriche ZZ_*

### Query Prometheus di esempio:
```promql
# Rate di registrazioni per tipo utente
rate(ZZ_user_registration_total[5m]) by (user_type)

# Percentile 95 delle durate API
histogram_quantile(0.95, rate(ZZ_api_call_duration_bucket[5m]))

# Totale email inviate per tipo
sum(rate(ZZ_email_sent_total[1h])) by (email_type)
```

Il POC è ora **perfetto per capire i dati** e dimostrare come funziona il sistema di metriche con la massima semplicità! 🚀
