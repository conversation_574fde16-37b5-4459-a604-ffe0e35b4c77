{"dashboard": {"id": null, "title": "Symfony OTLP Metrics POC", "tags": ["symfony", "otlp", "poc"], "style": "dark", "timezone": "browser", "refresh": "5s", "time": {"from": "now-15m", "to": "now"}, "panels": [{"id": 1, "title": "User Registration Events", "type": "stat", "targets": [{"expr": "rate(ZZ_user_registration_total[5m])", "legendFormat": "{{user_type}} - {{source}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"displayMode": "list", "orientation": "horizontal"}, "mappings": [], "thresholds": {"steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 0}}, {"id": 2, "title": "User Login Events", "type": "timeseries", "targets": [{"expr": "rate(ZZ_user_login_total[5m])", "legendFormat": "{{status}} - {{method}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 0}}, {"id": 3, "title": "<PERSON><PERSON>", "type": "barchart", "targets": [{"expr": "sum by (email_type, template) (rate(ZZ_email_sent_total[5m]))", "legendFormat": "{{email_type}} - {{template}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"hideFrom": {"legend": false, "tooltip": false, "vis": false}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 8}}, {"id": 4, "title": "Business Events", "type": "timeseries", "targets": [{"expr": "rate(ZZ_business_order_completed_total[5m])", "legendFormat": "{{payment_method}} - {{order_value}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "auto", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "reqps"}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 8}}, {"id": 5, "title": "API Call Duration (Histogram)", "type": "timeseries", "targets": [{"expr": "histogram_quantile(0.50, rate(ZZ_api_call_duration_bucket[5m]))", "legendFormat": "p50", "refId": "A"}, {"expr": "histogram_quantile(0.95, rate(ZZ_api_call_duration_bucket[5m]))", "legendFormat": "p95", "refId": "B"}, {"expr": "histogram_quantile(0.99, rate(ZZ_api_call_duration_bucket[5m]))", "legendFormat": "p99", "refId": "C"}], "fieldConfig": {"defaults": {"color": {"mode": "palette-classic"}, "custom": {"drawStyle": "line", "lineInterpolation": "linear", "barAlignment": 0, "lineWidth": 1, "fillOpacity": 10, "gradientMode": "none", "spanNulls": false, "insertNulls": false, "showPoints": "never", "pointSize": 5, "stacking": {"mode": "none", "group": "A"}, "axisPlacement": "auto", "axisLabel": "", "scaleDistribution": {"type": "linear"}, "hideFrom": {"legend": false, "tooltip": false, "vis": false}, "thresholdsStyle": {"mode": "off"}}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}, "unit": "ms"}}, "gridPos": {"h": 8, "w": 12, "x": 0, "y": 16}}, {"id": 6, "title": "Test Gauge Values", "type": "gauge", "targets": [{"expr": "ZZ_test_gauge", "legendFormat": "{{type}}", "refId": "A"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}, {"color": "red", "value": 80}]}, "unit": "short", "min": 0, "max": 100}}, "gridPos": {"h": 8, "w": 12, "x": 12, "y": 16}}, {"id": 7, "title": "All POC Metrics Overview", "type": "table", "targets": [{"expr": "group by (__name__) ({__name__=~\"ZZ_.*\"})", "legendFormat": "{{__name__}}", "refId": "A", "format": "table"}], "fieldConfig": {"defaults": {"color": {"mode": "thresholds"}, "custom": {"align": "auto", "displayMode": "auto"}, "mappings": [], "thresholds": {"mode": "absolute", "steps": [{"color": "green", "value": null}]}}}, "gridPos": {"h": 8, "w": 24, "x": 0, "y": 24}}], "schemaVersion": 27, "version": 1, "links": []}}