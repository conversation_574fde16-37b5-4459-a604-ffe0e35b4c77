<?php

namespace App\Service;

/**
 * Esempio di servizio che utilizza MetricsCollector per tracciare eventi di business
 */
class UserService
{
    public function __construct(
        private MetricsCollector $metricsCollector
    ) {}

    public function createUser(array $userData): array
    {
        $startTime = microtime(true);
        
        try {
            // Simula la creazione dell'utente
            $userId = uniqid('user_');
            
            // Logica di business per creare l'utente
            // ...
            
            $endTime = microtime(true);
            $durationMs = ($endTime - $startTime) * 1000;
            
            // Registra le metriche
            $this->metricsCollector->recordUserRegistration(
                $userData['type'] ?? 'standard',
                [
                    'source' => $userData['source'] ?? 'api',
                    'has_referral' => isset($userData['referral_code']) ? 'yes' : 'no'
                ]
            );
            
            $this->metricsCollector->recordDuration('user_creation', $durationMs);
            
            return [
                'success' => true,
                'user_id' => $userId,
                'duration_ms' => $durationMs
            ];
            
        } catch (\Exception $e) {
            // Registra gli errori
            $this->metricsCollector->recordEvent('user_creation_error', 1, [
                'error_type' => get_class($e),
                'user_type' => $userData['type'] ?? 'standard'
            ]);
            
            throw $e;
        }
    }

    public function updateUserProfile(string $userId, array $profileData): bool
    {
        try {
            // Simula l'aggiornamento del profilo
            // ...
            
            $this->metricsCollector->recordEvent('user_profile_updated', 1, [
                'fields_updated' => count($profileData),
                'has_avatar' => isset($profileData['avatar']) ? 'yes' : 'no'
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            $this->metricsCollector->recordEvent('user_profile_update_error', 1, [
                'error_type' => get_class($e)
            ]);
            
            return false;
        }
    }

    public function deleteUser(string $userId, string $reason = 'user_request'): bool
    {
        try {
            // Simula la cancellazione dell'utente
            // ...
            
            $this->metricsCollector->recordEvent('user_deleted', 1, [
                'deletion_reason' => $reason,
                'user_id_hash' => substr(md5($userId), 0, 8) // Hash per privacy
            ]);
            
            return true;
            
        } catch (\Exception $e) {
            $this->metricsCollector->recordEvent('user_deletion_error', 1, [
                'error_type' => get_class($e),
                'deletion_reason' => $reason
            ]);
            
            return false;
        }
    }
}
