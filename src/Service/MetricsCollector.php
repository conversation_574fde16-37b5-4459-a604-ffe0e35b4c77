<?php

namespace App\Service;

use OpenTelemetry\API\Metrics\MeterProviderInterface;
use OpenTelemetry\API\Metrics\MeterInterface;
use OpenTelemetry\API\Metrics\CounterInterface;
use OpenTelemetry\API\Metrics\HistogramInterface;
use OpenTelemetry\API\Metrics\GaugeInterface;
use OpenTelemetry\SDK\Metrics\MeterProvider;
use OpenTelemetry\SDK\Metrics\MetricExporter\ConsoleMetricExporter;
use OpenTelemetry\Contrib\Otlp\MetricExporter;
use OpenTelemetry\SDK\Metrics\MetricReader\ExportingReader;
use OpenTelemetry\SDK\Resource\ResourceInfo;
use OpenTelemetry\SDK\Common\Attribute\Attributes;
use OpenTelemetry\SemConv\ResourceAttributes;
use OpenTelemetry\Contrib\Grpc\GrpcTransportFactory;
use OpenTelemetry\Contrib\Otlp\OtlpUtil;
use Psr\Log\LoggerInterface;

/**
 * Servizio per raccogliere metriche di business logic e inviarle a Prometheus via OTLP
 *
 * Uso semplice tipo logger:
 * - $metricsCollector->recordEvent('user_registration', 1, ['user_type' => 'premium']);
 * - $metricsCollector->recordEvent('email_sent', 1, ['email_type' => 'welcome']);
 * - $metricsCollector->recordEvent('login_attempt', 1, ['status' => 'success']);
 */
class MetricsCollector
{
    private MeterInterface $meter;
    private LoggerInterface $logger;
    private array $counters = [];
    private array $histograms = [];
    private array $gauges = [];

    public function __construct(LoggerInterface $logger)
    {
        $this->logger = $logger;
        $this->initializeMeter();
    }

    private function initializeMeter(): void
    {
        try {
            // Crea le risorse per identificare il servizio
            $resource = ResourceInfo::create(Attributes::create([
                ResourceAttributes::SERVICE_NAME => $_ENV['OTEL_SERVICE_NAME'] ?? 'symfony-app',
                ResourceAttributes::SERVICE_VERSION => $_ENV['OTEL_SERVICE_VERSION'] ?? '1.0.0',
            ]));

            // Configura l'esportatore in base alle variabili di ambiente
            $otlpEndpoint = $_ENV['OTEL_EXPORTER_OTLP_ENDPOINT'] ?? null;

            if ($otlpEndpoint) {
                // Usa l'esportatore OTLP per Prometheus
                $this->logger->info('Using OTLP exporter', ['endpoint' => $otlpEndpoint]);

                // Crea il transport per OTLP
                $transport = (new GrpcTransportFactory())->create($otlpEndpoint, 'application/x-protobuf');
                $exporter = new MetricExporter($transport);
            } else {
                // Fallback al console exporter per debug
                $this->logger->info('Using Console exporter for debug');
                $exporter = new ConsoleMetricExporter();
            }

            $reader = new ExportingReader($exporter);

            // Crea il MeterProvider
            $meterProvider = MeterProvider::builder()
                ->setResource($resource)
                ->addReader($reader)
                ->build();

            $this->meter = $meterProvider->getMeter('app-business-metrics', '1.0.0');

            $this->logger->info('MetricsCollector initialized successfully');

        } catch (\Exception $e) {
            $this->logger->error('Failed to initialize MetricsCollector', [
                'error' => $e->getMessage()
            ]);

            // Fallback: crea un meter provider vuoto per evitare errori
            $meterProvider = MeterProvider::builder()->build();
            $this->meter = $meterProvider->getMeter('app-business-metrics', '1.0.0');
        }
    }

    /**
     * Registra un evento di business logic come metrica
     *
     * @param string $metricName Nome della metrica (es: 'user_registration', 'email_sent', 'login_attempt')
     * @param float|int $value Valore della metrica (default: 1 per contatori)
     * @param array $attributes Metadati/attributi per filtrare (es: ['user_type' => 'premium', 'status' => 'success'])
     * @param string $type Tipo di metrica: 'counter', 'histogram', 'gauge' (default: 'counter')
     * @param string|null $description Descrizione della metrica
     */
    public function recordEvent(
        string $metricName,
        float|int $value = 1,
        array $attributes = [],
        string $type = 'counter',
        ?string $description = null
    ): void {
        try {
            $description = $description ?? "Business event: {$metricName}";

            switch ($type) {
                case 'counter':
                    $this->recordCounter($metricName, $value, $attributes, $description);
                    break;

                case 'histogram':
                    $this->recordHistogram($metricName, $value, $attributes, $description);
                    break;

                case 'gauge':
                    $this->recordGauge($metricName, $value, $attributes, $description);
                    break;

                default:
                    throw new \InvalidArgumentException("Unsupported metric type: {$type}");
            }

            $this->logger->debug('Metric recorded', [
                'metric_name' => $metricName,
                'value' => $value,
                'attributes' => $attributes,
                'type' => $type
            ]);

        } catch (\Exception $e) {
            $this->logger->error('Failed to record metric', [
                'metric_name' => $metricName,
                'error' => $e->getMessage(),
                'attributes' => $attributes
            ]);
        }
    }

    /**
     * Metodi di convenienza per eventi comuni
     */

    public function recordUserRegistration(string $userType = 'standard', array $additionalAttributes = []): void
    {
        $attributes = array_merge(['user_type' => $userType], $additionalAttributes);
        $this->recordEvent('user_registration', 1, $attributes, 'counter', 'User registration events');
    }

    public function recordLogin(string $status = 'success', array $additionalAttributes = []): void
    {
        $attributes = array_merge(['status' => $status], $additionalAttributes);
        $this->recordEvent('user_login', 1, $attributes, 'counter', 'User login attempts');
    }

    public function recordEmailSent(string $emailType, array $additionalAttributes = []): void
    {
        $attributes = array_merge(['email_type' => $emailType], $additionalAttributes);
        $this->recordEvent('email_sent', 1, $attributes, 'counter', 'Email sending events');
    }

    public function recordBusinessEvent(string $eventName, float|int $value = 1, array $attributes = []): void
    {
        $this->recordEvent("business_{$eventName}", $value, $attributes, 'counter', "Business event: {$eventName}");
    }

    /**
     * Registra una durata (utile per performance monitoring)
     */
    public function recordDuration(string $operation, float $durationMs, array $attributes = []): void
    {
        $this->recordEvent("{$operation}_duration", $durationMs, $attributes, 'histogram', "Duration of {$operation} in milliseconds");
    }

    private function recordCounter(string $name, float|int $value, array $attributes, string $description): void
    {
        if (!isset($this->counters[$name])) {
            $this->counters[$name] = $this->meter->createCounter($name, null, $description);
        }

        $this->counters[$name]->add($value, $attributes);
    }

    private function recordHistogram(string $name, float|int $value, array $attributes, string $description): void
    {
        if (!isset($this->histograms[$name])) {
            $this->histograms[$name] = $this->meter->createHistogram($name, null, $description);
        }

        $this->histograms[$name]->record($value, $attributes);
    }

    private function recordGauge(string $name, float|int $value, array $attributes, string $description): void
    {
        if (!isset($this->gauges[$name])) {
            $this->gauges[$name] = $this->meter->createGauge($name, null, $description);
        }

        $this->gauges[$name]->record($value, $attributes);
    }
}
