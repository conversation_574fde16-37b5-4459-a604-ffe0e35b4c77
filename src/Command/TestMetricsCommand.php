<?php

namespace App\Command;

use App\Service\MetricsCollector;
use Symfony\Component\Console\Attribute\AsCommand;
use Symfony\Component\Console\Command\Command;
use Symfony\Component\Console\Input\InputInterface;
use Symfony\Component\Console\Output\OutputInterface;
use Symfony\Component\Console\Style\SymfonyStyle;

#[AsCommand(
    name: 'app:test-metrics',
    description: 'Test the metrics collection system'
)]
class TestMetricsCommand extends Command
{
    public function __construct(
        private MetricsCollector $metricsCollector
    ) {
        parent::__construct();
    }

    protected function execute(InputInterface $input, OutputInterface $output): int
    {
        $io = new SymfonyStyle($input, $output);

        $io->title('Testing Metrics Collection System');

        try {
            // Test counter metrics
            $io->section('Testing Counter Metrics');
            $this->metricsCollector->recordEvent('ZZ_user_registration', 1, [
                'user_type' => 'premium',
                'source' => 'console_test',
                'country' => 'IT'
            ]);
            $io->success('✓ User registration metric recorded');

            $this->metricsCollector->recordEvent('ZZ_user_login', 1, [
                'status' => 'success',
                'method' => 'console_test'
            ]);
            $io->success('✓ Login success metric recorded');

            $this->metricsCollector->recordEvent('ZZ_email_sent', 1, [
                'email_type' => 'welcome',
                'template' => 'test_template'
            ]);
            $io->success('✓ Email sent metric recorded');

            // Test business events
            $io->section('Testing Business Events');
            $this->metricsCollector->recordEvent('ZZ_business_order_completed', 1, [
                'payment_method' => 'test_card',
                'order_value' => 'high'
            ]);
            $io->success('✓ Business event metric recorded');

            // Test histogram metrics
            $io->section('Testing Histogram Metrics');
            $this->metricsCollector->recordEvent('ZZ_api_call_duration', 125.5, [
                'endpoint' => '/test'
            ], 'histogram');
            $io->success('✓ Duration metric recorded');

            // Test different metric types
            $io->section('Testing Different Metric Types');
            $this->metricsCollector->recordEvent('ZZ_test_counter', 5, ['type' => 'counter_test'], 'counter');
            $this->metricsCollector->recordEvent('ZZ_test_histogram', 42.7, ['type' => 'histogram_test'], 'histogram');
            $this->metricsCollector->recordEvent('ZZ_test_gauge', 15, ['type' => 'gauge_test'], 'gauge');
            $io->success('✓ All metric types recorded');

            $io->success('All metrics have been sent successfully!');

            $endpoint = $_ENV['OTEL_EXPORTER_OTLP_ENDPOINT'] ?? 'console (debug mode)';
            $io->note("Metrics sent to: {$endpoint}");

            return Command::SUCCESS;

        } catch (\Exception $e) {
            $io->error('Failed to send metrics: ' . $e->getMessage());
            return Command::FAILURE;
        }
    }
}
