<?php

namespace App\Controller;

use App\Service\MetricsCollector;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\JsonResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\Routing\Annotation\Route;

/**
 * Controller di esempio che mostra come utilizzare il MetricsCollector
 * per registrare eventi di business logic
 */
class ExampleController extends AbstractController
{
    public function __construct(
        private MetricsCollector $metricsCollector
    ) {}

    #[Route('/api/user/register', name: 'user_register', methods: ['POST'])]
    public function registerUser(Request $request): JsonResponse
    {
        // Simula la logica di registrazione utente
        $userData = json_decode($request->getContent(), true);
        $userType = $userData['type'] ?? 'standard';

        try {
            // Simula la registrazione dell'utente
            $userId = rand(1000, 9999);

            // Registra la metrica di registrazione utente
            $this->metricsCollector->recordEvent('user_registration', 1, [
                'user_type' => $userType,
                'registration_source' => $userData['source'] ?? 'web',
                'country' => $userData['country'] ?? 'unknown'
            ]);

            return new JsonResponse([
                'success' => true,
                'user_id' => $userId,
                'message' => 'User registered successfully'
            ]);

        } catch (\Exception $e) {
            // Registra anche i fallimenti
            $this->metricsCollector->recordEvent('user_registration_failed', 1, [
                'user_type' => $userType,
                'error_type' => 'registration_error'
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'Registration failed'
            ], 500);
        }
    }

    #[Route('/api/user/login', name: 'user_login', methods: ['POST'])]
    public function loginUser(Request $request): JsonResponse
    {
        $loginData = json_decode($request->getContent(), true);
        $email = $loginData['email'] ?? '';

        // Simula la logica di login
        $loginSuccess = !empty($email) && str_contains($email, '@');

        if ($loginSuccess) {
            // Login riuscito
            $this->metricsCollector->recordEvent('user_login', 1, [
                'status' => 'success',
                'login_method' => $loginData['method'] ?? 'email',
                'user_agent' => $request->headers->get('User-Agent', 'unknown')
            ]);

            return new JsonResponse([
                'success' => true,
                'message' => 'Login successful'
            ]);
        } else {
            // Login fallito
            $this->metricsCollector->recordEvent('user_login', 1, [
                'status' => 'failed',
                'login_method' => $loginData['method'] ?? 'email',
                'failure_reason' => 'invalid_credentials'
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'Invalid credentials'
            ], 401);
        }
    }

    #[Route('/api/email/send', name: 'send_email', methods: ['POST'])]
    public function sendEmail(Request $request): JsonResponse
    {
        $emailData = json_decode($request->getContent(), true);
        $emailType = $emailData['type'] ?? 'generic';

        try {
            // Simula l'invio dell'email
            $emailId = uniqid('email_');

            // Registra la metrica di invio email
            $this->metricsCollector->recordEvent('email_sent', 1, [
                'email_type' => $emailType,
                'recipient_type' => $emailData['recipient_type'] ?? 'user',
                'template' => $emailData['template'] ?? 'default',
                'priority' => $emailData['priority'] ?? 'normal'
            ]);

            return new JsonResponse([
                'success' => true,
                'email_id' => $emailId,
                'message' => 'Email sent successfully'
            ]);

        } catch (\Exception $e) {
            // Registra i fallimenti di invio email
            $this->metricsCollector->recordEvent('email_send_failed', 1, [
                'email_type' => $emailType,
                'error_type' => 'smtp_error'
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'Failed to send email'
            ], 500);
        }
    }

    #[Route('/api/business/action', name: 'business_action', methods: ['POST'])]
    public function businessAction(Request $request): JsonResponse
    {
        $actionData = json_decode($request->getContent(), true);
        $actionName = $actionData['action'] ?? 'unknown';

        // Misura il tempo di esecuzione
        $startTime = microtime(true);

        try {
            // Simula un'azione di business
            usleep(rand(10000, 100000)); // Simula elaborazione da 10ms a 100ms

            $endTime = microtime(true);
            $durationMs = ($endTime - $startTime) * 1000;

            // Registra l'evento di business
            $this->metricsCollector->recordEvent("business_{$actionName}", 1, [
                'category' => $actionData['category'] ?? 'general',
                'user_id' => $actionData['user_id'] ?? 'anonymous',
                'success' => true
            ]);

            // Registra anche la durata
            $this->metricsCollector->recordEvent("{$actionName}_duration", $durationMs, [
                'category' => $actionData['category'] ?? 'general'
            ], 'histogram');

            return new JsonResponse([
                'success' => true,
                'action' => $actionName,
                'duration_ms' => round($durationMs, 2),
                'message' => 'Action completed successfully'
            ]);

        } catch (\Exception $e) {
            $endTime = microtime(true);
            $durationMs = ($endTime - $startTime) * 1000;

            // Registra il fallimento
            $this->metricsCollector->recordEvent("business_{$actionName}", 1, [
                'category' => $actionData['category'] ?? 'general',
                'user_id' => $actionData['user_id'] ?? 'anonymous',
                'success' => false,
                'error_type' => 'execution_error'
            ]);

            return new JsonResponse([
                'success' => false,
                'message' => 'Action failed'
            ], 500);
        }
    }

    #[Route('/api/metrics/test', name: 'test_metrics', methods: ['GET'])]
    public function testMetrics(): JsonResponse
    {
        // Genera alcune metriche di test
        $this->metricsCollector->recordEvent('test_counter', 1, ['test_type' => 'api_test']);
        $this->metricsCollector->recordEvent('test_histogram', rand(1, 100), ['test_type' => 'performance'], 'histogram');
        $this->metricsCollector->recordEvent('test_gauge', rand(0, 50), ['test_type' => 'current_value'], 'gauge');

        return new JsonResponse([
            'success' => true,
            'message' => 'Test metrics sent to Prometheus via OTLP'
        ]);
    }
}
