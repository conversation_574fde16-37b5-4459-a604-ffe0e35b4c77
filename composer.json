{"type": "project", "license": "proprietary", "minimum-stability": "stable", "prefer-stable": true, "require": {"php": ">=8.2", "ext-ctype": "*", "ext-iconv": "*", "guzzlehttp/promises": "^2.3", "nyholm/psr7": "^1.8", "open-telemetry/symfony-sdk-bundle": "^0.0.28", "php-http/httplug": "^2.4", "symfony/console": "7.3.*", "symfony/dotenv": "7.3.*", "symfony/flex": "^2", "symfony/framework-bundle": "7.3.*", "symfony/http-client": "^7.3", "symfony/runtime": "7.3.*", "symfony/yaml": "7.3.*"}, "config": {"allow-plugins": {"php-http/discovery": true, "symfony/flex": true, "symfony/runtime": true, "tbachert/spi": true}, "bump-after-update": true, "sort-packages": true}, "autoload": {"psr-4": {"App\\": "src/"}}, "autoload-dev": {"psr-4": {"App\\Tests\\": "tests/"}}, "replace": {"symfony/polyfill-ctype": "*", "symfony/polyfill-iconv": "*", "symfony/polyfill-php72": "*", "symfony/polyfill-php73": "*", "symfony/polyfill-php74": "*", "symfony/polyfill-php80": "*", "symfony/polyfill-php81": "*", "symfony/polyfill-php82": "*"}, "scripts": {"auto-scripts": {"cache:clear": "symfony-cmd", "assets:install %PUBLIC_DIR%": "symfony-cmd"}, "post-install-cmd": ["@auto-scripts"], "post-update-cmd": ["@auto-scripts"]}, "conflict": {"symfony/symfony": "*"}, "extra": {"symfony": {"allow-contrib": false, "require": "7.3.*"}}}